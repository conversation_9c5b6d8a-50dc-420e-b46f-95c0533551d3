<script lang="ts" setup>
import type { InputProps } from '../../types/components/input'
import { useModelValue } from '../../composables/useModelValue'
import PInput from '../input/index.vue'

interface Props {
  min?: number
  max?: number
  step?: number
  scientific?: boolean
  modelValue?:  string | number | null
}

defineOptions({
  name: 'PNumberInput',
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
})

const props = withDefaults(
  defineProps<Props>(),
  {
    step: 1,
    min: Number.MIN_SAFE_INTEGER,
    max: Number.MAX_SAFE_INTEGER,
    scientific: true,
  },
)

const emits = defineEmits<{
  'update:modelValue': [number]
}>()

const computedModelValue = useModelValue(props, emits, {
  get: (): number => {
    return formatModelValue(props.modelValue)!
  },
})

function formatModelValue<T>(value: T): number {
  const digitizeValue = Number(value)

  if (Number.isNaN(digitizeValue)) {
    return 0
  }

  return digitizeValue
}

function onInputChange(ev: InputProps['modelValue']) {
  const digitizeValue = formatModelValue(ev)
}

function increaseValue(value: number) {
  return value + props.step
}

function decreaseValue(value: number) {
  return value - props.step
}

const INTEGER_REGEX = /^-?\d+$/
const INTEGER_REGEX_WITH_SCIENTIFIC = /^-?\d+\.?\d*(e-?\d+)?$/

function onInputKeydown(ev: KeyboardEvent) {
  if (ev.key === 'ArrowUp') {
    ev.preventDefault()
    computedModelValue.value = increaseValue(computedModelValue.value)
  } else if (ev.key === 'ArrowDown') {
    ev.preventDefault()
    computedModelValue.value = decreaseValue(computedModelValue.value)
  }
}
</script>

<template>
  {{ computedModelValue }} - {{ typeof computedModelValue }}
  <PInput
    inputmode="decimal"
    v-bind="$attrs"
    :min="min"
    :max="max"
    :model-value="computedModelValue"
    @keydown="onInputKeydown"
  />
</template>
