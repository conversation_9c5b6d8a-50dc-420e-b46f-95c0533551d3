{"name": "pxd", "type": "module", "version": "0.0.27", "packageManager": "pnpm@9.15.9", "description": "A universal UI component library for Vue2&3.", "author": "lib<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/libondev/pxd#readme", "repository": {"type": "git", "url": "git+https://github.com/libondev/pxd.git"}, "bugs": {"url": "https://github.com/libondev/pxd/issues"}, "keywords": ["ui", "vue", "pxd", "vue2", "vue3", "components", "ui framework", "component library"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./resolver": {"types": "./dist/plugins/resolver.d.ts", "default": "./dist/plugins/resolver.js"}, "./components": {"types": "./dist/components/index.d.ts", "default": "./dist/components/index.js"}, "./composables": {"types": "./dist/composables/index.d.ts", "default": "./dist/composables/index.js"}, "./locales": {"types": "./dist/locales/index.d.ts", "default": "./dist/locales/index.js"}, "./tw.css": "./dist/styles/tw.css", "./styles.css": "./dist/styles/styles.css", "./components/*": {"types": "./dist/components/*/index.vue.d.ts", "default": "./dist/components/*/index.vue"}, "./composables/*": {"types": "./dist/composables/*.d.ts", "default": "./dist/composables/*.js"}, "./contexts/*": {"types": "./dist/contexts/*.d.ts", "default": "./dist/contexts/*.js"}, "./locales/*": {"types": "./dist/locales/*.d.ts", "default": "./dist/locales/*.js"}, "./types/*": "./dist/types/*.d.ts", "./utils/*": {"types": "./dist/utils/*.d.ts", "default": "./dist/utils/*.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "style": "./dist/styles/index.css", "typesVersions": {"*": {"*": ["*", "dist/*", "dist/*.d.ts", "dist/*/index.d.ts"]}}, "files": ["dist"], "scripts": {"dev": "run-s dev:lib dev:docs", "dev:lib": "unbuild --stub", "dev:docs": "pnpm --filter docs dev", "build": "run-s update-exports build:lib build:docs", "build:only": "run-s build:lib build:docs", "build:lib": "unbuild", "build:docs": "pnpm --filter docs build", "build:style": "run-s gen-styles-file gen-tw-css-file", "build:types": "vue-tsc -p tsconfig.type.json", "preview": "pnpm --filter docs preview", "update-exports": "node scripts/update-exports.js", "gen-styles-file": "node scripts/gen-css.js", "gen-tw-css-file": "tailwindcss -i ./src/styles/styles.css -o ./dist/styles/styles.css -m", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest run", "test:watch": "vitest", "typecheck": "vue-tsc -p ./tsconfig.app.json --noEmit", "bump": "pnpx bumpp", "prepare": "node scripts/setup-scripts.js", "postversion": "node scripts/update-exports.js --stage", "prepublishOnly": "run-s update-exports build:lib build:style build:types"}, "peerDependencies": {"vue": ">=2.7.0 || >=3.2.0"}, "dependencies": {"@gdsicon/vue": "catalog:"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@tailwindcss/cli": "catalog:", "@tsconfig/node22": "catalog:", "@types/node": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vue/test-utils": "catalog:", "@vue/tsconfig": "catalog:", "eslint": "catalog:", "eslint-plugin-better-tailwindcss": "catalog:", "happy-dom": "catalog:", "husky": "catalog:", "lint-staged": "catalog:", "mkdist": "catalog:", "npm-run-all2": "catalog:", "tinyglobby": "catalog:", "typescript": "catalog:", "unbuild": "catalog:", "unplugin-vue-components": "catalog:", "vitest": "catalog:", "vue": "catalog:", "vue-sfc-transformer": "catalog:", "vue-tsc": "catalog:"}, "lint-staged": {"*.{ts,vue}": ["eslint --fix", "vue-tsc -p ./tsconfig.app.json --noEmit"]}}